const fs = require('fs');
const path = require('path');
const { getS3Instance, hasAWSCredentials, getFileUrl } = require('./storageHelper');

/**
 * Assemble uploaded chunks into a final file
 * @param {Object} uploadSession - Upload session containing chunk information
 * @returns {Object} - Result with final file URL and metadata
 */
const assembleChunks = async (uploadSession) => {
  const { uploadId, fileName, fileSize, fileType, uploadedChunks, totalChunks } = uploadSession;
  
  console.log(`[ChunkAssembler] Starting assembly for ${fileName} (${totalChunks} chunks)`);

  // Sort chunks by index to ensure correct order
  const sortedChunks = uploadedChunks
    .filter(chunk => chunk) // Remove empty slots
    .sort((a, b) => a.chunkIndex - b.chunkIndex);

  if (sortedChunks.length !== totalChunks) {
    throw new Error(`Missing chunks: expected ${totalChunks}, got ${sortedChunks.length}`);
  }

  // Debug: Log first few chunk paths to understand the URL structure
  console.log(`[ChunkAssembler] Sample chunk paths:`);
  sortedChunks.slice(0, 3).forEach((chunk, index) => {
    console.log(`  Chunk ${chunk.chunkIndex}: ${chunk.tempPath}`);
  });
  
  const timestamp = Date.now();
  const sanitizedName = fileName.replace(/[^a-zA-Z0-9.-]/g, '-');
  const finalFileName = `${timestamp}-${sanitizedName}`;
  
  if (hasAWSCredentials()) {
    return await assembleChunksS3(sortedChunks, finalFileName, fileType, uploadId);
  } else {
    return await assembleChunksLocal(sortedChunks, finalFileName, fileType, uploadId);
  }
};

/**
 * Assemble chunks for S3 storage
 */
const assembleChunksS3 = async (sortedChunks, finalFileName, fileType, uploadId) => {
  const s3 = getS3Instance();
  if (!s3) {
    throw new Error('S3 not configured');
  }
  
  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for S3 upload`);

  // First, let's list what's actually in the S3 bucket to debug
  try {
    const listParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Prefix: 'uploads/chunk/',
      MaxKeys: 10
    };
    const listResult = await s3.listObjectsV2(listParams).promise();
    console.log(`[ChunkAssembler] Found ${listResult.Contents.length} objects in uploads/chunk/:`);
    listResult.Contents.slice(0, 5).forEach(obj => {
      console.log(`  - ${obj.Key} (${obj.Size} bytes)`);
    });
  } catch (listError) {
    console.error('[ChunkAssembler] Error listing S3 objects:', listError);
  }

  // For S3, we need to use multipart upload completion
  // First, create a multipart upload
  const s3Key = `uploads/content/${finalFileName}`;

  const createParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: s3Key,
    ContentType: fileType,
    Metadata: {
      originalName: finalFileName,
      uploadId: uploadId,
      assembledAt: Date.now().toString()
    }
  };
  
  let multipartUpload = null;

  try {
    multipartUpload = await s3.createMultipartUpload(createParams).promise();
    const uploadId_s3 = multipartUpload.UploadId;
    
    console.log(`[ChunkAssembler] Created S3 multipart upload: ${uploadId_s3}`);
    
    // Upload each chunk as a part
    const uploadPromises = sortedChunks.map(async (chunk, index) => {
      const partNumber = index + 1;
      
      // Read chunk data
      let chunkData;
      if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
        // Chunk is already in S3, get it
        // Extract key from URL: https://bucket.s3.region.amazonaws.com/uploads/chunk/filename
        const url = new URL(chunk.tempPath);
        const chunkKey = url.pathname.substring(1); // Remove leading slash

        console.log(`[ChunkAssembler] Getting chunk ${index + 1} from S3 key: ${chunkKey}`);

        const getParams = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: chunkKey
        };

        try {
          const chunkObject = await s3.getObject(getParams).promise();
          chunkData = chunkObject.Body;
          console.log(`[ChunkAssembler] Successfully retrieved chunk ${index + 1} (${chunkData.length} bytes)`);
        } catch (error) {
          console.error(`[ChunkAssembler] Error getting chunk ${index + 1} from S3:`, error.code, error.message);
          console.error(`[ChunkAssembler] Chunk URL: ${chunk.tempPath}`);
          console.error(`[ChunkAssembler] Extracted key: ${chunkKey}`);
          console.error(`[ChunkAssembler] Bucket: ${process.env.AWS_BUCKET_NAME}`);

          // Try to list objects with this prefix to see what's actually there
          try {
            const listParams = {
              Bucket: process.env.AWS_BUCKET_NAME,
              Prefix: chunkKey.substring(0, chunkKey.lastIndexOf('/') + 1),
              MaxKeys: 10
            };
            const listResult = await s3.listObjectsV2(listParams).promise();
            console.error(`[ChunkAssembler] Objects with similar prefix:`);
            listResult.Contents.forEach(obj => {
              console.error(`  - ${obj.Key}`);
            });
          } catch (listError) {
            console.error(`[ChunkAssembler] Could not list objects:`, listError.message);
          }

          throw error;
        }
      } else {
        // Chunk is local file
        chunkData = fs.readFileSync(chunk.tempPath);
        console.log(`[ChunkAssembler] Read local chunk ${index + 1} (${chunkData.length} bytes)`);
      }
      
      const uploadPartParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: s3Key,
        PartNumber: partNumber,
        UploadId: uploadId_s3,
        Body: chunkData
      };
      
      const result = await s3.uploadPart(uploadPartParams).promise();
      console.log(`[ChunkAssembler] Uploaded part ${partNumber}/${sortedChunks.length}`);
      
      return {
        ETag: result.ETag,
        PartNumber: partNumber
      };
    });
    
    const parts = await Promise.all(uploadPromises);
    
    // Complete the multipart upload
    const completeParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: s3Key,
      UploadId: uploadId_s3,
      MultipartUpload: {
        Parts: parts
      }
    };
    
    const finalResult = await s3.completeMultipartUpload(completeParams).promise();
    
    // Clean up temporary chunks
    await cleanupTempChunks(sortedChunks);
    
    console.log(`[ChunkAssembler] S3 assembly completed: ${finalResult.Location}`);
    
    return {
      fileUrl: finalResult.Location,
      bucket: finalResult.Bucket,
      key: finalResult.Key,
      etag: finalResult.ETag
    };
    
  } catch (error) {
    console.error('[ChunkAssembler] S3 assembly error:', error);
    
    // Clean up failed multipart upload if it exists
    if (multipartUpload && multipartUpload.UploadId) {
      try {
        await s3.abortMultipartUpload({
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: s3Key,
          UploadId: multipartUpload.UploadId
        }).promise();
        console.log('[ChunkAssembler] Aborted failed multipart upload');
      } catch (abortError) {
        console.error('[ChunkAssembler] Error aborting multipart upload:', abortError);
      }
    }
    
    throw error;
  }
};

/**
 * Assemble chunks for local storage
 */
const assembleChunksLocal = async (sortedChunks, finalFileName, fileType, uploadId) => {
  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for local storage`);
  
  const uploadsDir = './uploads';
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  const finalFilePath = path.join(uploadsDir, finalFileName);
  const writeStream = fs.createWriteStream(finalFilePath);
  
  try {
    // Write chunks in order
    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i];
      console.log(`[ChunkAssembler] Writing chunk ${i + 1}/${sortedChunks.length}`);
      
      const chunkData = fs.readFileSync(chunk.tempPath);
      writeStream.write(chunkData);
    }
    
    writeStream.end();
    
    // Wait for write to complete
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
    
    // Clean up temporary chunks
    await cleanupTempChunks(sortedChunks);
    
    console.log(`[ChunkAssembler] Local assembly completed: ${finalFilePath}`);
    
    return {
      fileUrl: `/uploads/${finalFileName}`,
      filename: finalFileName,
      path: finalFilePath
    };
    
  } catch (error) {
    console.error('[ChunkAssembler] Local assembly error:', error);
    
    // Clean up partial file
    if (fs.existsSync(finalFilePath)) {
      fs.unlinkSync(finalFilePath);
    }
    
    throw error;
  }
};

/**
 * Clean up temporary chunk files
 */
const cleanupTempChunks = async (chunks) => {
  console.log(`[ChunkAssembler] Cleaning up ${chunks.length} temporary chunks`);
  
  const s3 = getS3Instance();
  
  for (const chunk of chunks) {
    try {
      if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
        // S3 chunk - delete from S3
        if (s3) {
          const url = new URL(chunk.tempPath);
          const chunkKey = url.pathname.substring(1); // Remove leading slash
          await s3.deleteObject({
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: chunkKey
          }).promise();
          console.log(`[ChunkAssembler] Deleted S3 chunk: ${chunkKey}`);
        }
      } else {
        // Local chunk - delete file
        if (fs.existsSync(chunk.tempPath)) {
          fs.unlinkSync(chunk.tempPath);
          console.log(`[ChunkAssembler] Deleted local chunk: ${chunk.tempPath}`);
        }
      }
    } catch (error) {
      console.error(`[ChunkAssembler] Error cleaning up chunk ${chunk.tempPath}:`, error);
      // Continue with other chunks even if one fails
    }
  }
};

module.exports = {
  assembleChunks,
  cleanupTempChunks
};
