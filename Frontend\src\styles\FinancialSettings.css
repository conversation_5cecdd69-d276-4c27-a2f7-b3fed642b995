/**/
/* FinancialSettings Component Styles */
.FinancialSettings {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

.FinancialSettings .settings-section {
  background-color: var(--white);
  border-radius: var(--border-radius);

  overflow: hidden;
}

.FinancialSettings .section-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.FinancialSettings .section-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.FinancialSettings .section-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Settings Form */
.FinancialSettings .settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Settings Groups */
.FinancialSettings .settings-group {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--bg-gray);
}

.FinancialSettings .settings-group h4 {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.FinancialSettings .group-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
}

/* Form Elements */
.FinancialSettings .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  margin-bottom: var(--basefont);
}

.FinancialSettings .form-row:last-child {
  margin-bottom: 0;
}

.FinancialSettings .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.FinancialSettings .form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.FinancialSettings .form-input,
.FinancialSettings .form-select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.FinancialSettings .form-input:focus,
.FinancialSettings .form-select:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.FinancialSettings .form-input:read-only {
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  cursor: not-allowed;
}

/* Input with Icon */
.FinancialSettings .input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.FinancialSettings .input-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  z-index: 1;
}

.FinancialSettings .input-with-icon .form-input {
  padding-left: 32px;
}

/* Form Help Text */
.FinancialSettings .form-help {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-style: italic;
}

/* Commission Preview */
.FinancialSettings .commission-preview {
  padding: var(--basefont);
  border: 2px solid var(--btn-color);
  border-radius: var(--border-radius);
  background-color: var(--bg-blue);
}

.FinancialSettings .commission-preview h4 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.FinancialSettings .preview-example {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--basefont);
}

.FinancialSettings .example-sale {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.FinancialSettings .sale-label {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.FinancialSettings .breakdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.FinancialSettings .breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.FinancialSettings .breakdown-item.total {
  font-weight: 600;
  font-size: var(--basefont);
  color: var(--secondary-color);
  padding-top: var(--smallfont);
  border-top: 1px solid var(--light-gray);
  margin-top: var(--smallfont);
}

.FinancialSettings .breakdown-item .amount {
  font-weight: 600;
  color: var(--btn-color);
}

.FinancialSettings .breakdown-item.total .amount {
  color: #10b981;
  font-size: var(--basefont);
}

/* Form Actions */
.FinancialSettings .form-actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-end;
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

/* Buttons */
.FinancialSettings .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.FinancialSettings .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.FinancialSettings .btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.FinancialSettings .btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.FinancialSettings .btn.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
}

/* Responsive styles */
@media (max-width: 768px) {
  .FinancialSettings .form-row {
    grid-template-columns: 1fr;
  }

  .FinancialSettings .settings-form {
    padding: 0;
  }

  .FinancialSettings .form-actions {
    flex-direction: column;
  }

  .FinancialSettings .form-actions .btn {
    justify-content: center;
  }

  .FinancialSettings .breakdown-item {
    font-size: var(--extrasmallfont);
  }

  .FinancialSettings .sale-label {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .FinancialSettings .section-header {
    padding: var(--basefont);
  }

  .FinancialSettings .settings-group {
    padding: var(--smallfont);
  }

  .FinancialSettings .commission-preview {
    padding: var(--smallfont);
  }

  .FinancialSettings .preview-example {
    padding: var(--smallfont);
  }

  .FinancialSettings .breakdown-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .FinancialSettings .breakdown-item .amount {
    align-self: flex-end;
  }
}

/* Animation for changes */
.FinancialSettings .form-input.changed,
.FinancialSettings .form-select.changed {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Validation states */
.FinancialSettings .form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.FinancialSettings .form-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Loading state */
.FinancialSettings .form-actions.loading .btn {
  position: relative;
  color: transparent;
}

.FinancialSettings .form-actions.loading .btn::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
